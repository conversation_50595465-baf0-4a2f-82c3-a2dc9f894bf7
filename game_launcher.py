import tkinter as tk
from tkinter import ttk, messagebox
import requests
import os
import threading
from PIL import Image, ImageTk
import gdown
import zipfile
from launcher_config import COLORS, WINDOW_CONFIG, IMAGE_PATHS, FONT_PATHS, GAME_INFO, MESSAGES

class GameLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title(WINDOW_CONFIG['title'])
        self.root.geometry(WINDOW_CONFIG['size'])
        self.root.configure(bg=COLORS['primary_bg'])
        self.root.resizable(WINDOW_CONFIG['resizable'], WINDOW_CONFIG['resizable'])

        # تحديد مسار الخطوط
        self.font_path = FONT_PATHS['regular']
        self.bold_font_path = FONT_PATHS['bold']

        # متغيرات التحميل
        self.download_url = GAME_INFO['download_url']
        self.file_id = GAME_INFO['file_id']
        self.download_path = os.path.join(os.path.expanduser("~"), "Downloads")

        self.setup_ui()
        
    def setup_ui(self):
        # إطار رئيسي
        main_frame = tk.Frame(self.root, bg=COLORS['primary_bg'])
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # تحميل وعرض لوجو الاستوديو
        try:
            studio_logo = Image.open(IMAGE_PATHS['studio_logo'])
            studio_logo = studio_logo.resize((150, 150), Image.Resampling.LANCZOS)
            self.studio_logo_img = ImageTk.PhotoImage(studio_logo)

            studio_label = tk.Label(main_frame, image=self.studio_logo_img, bg=COLORS['primary_bg'])
            studio_label.pack(pady=(0, 10))
        except Exception as e:
            print(f"خطأ في تحميل لوجو الاستوديو: {e}")

        # عنوان الاستوديو
        studio_title = tk.Label(main_frame, text=GAME_INFO['studio'],
                               font=("Arial", 16, "bold"),
                               fg=COLORS['accent_color'], bg=COLORS['primary_bg'])
        studio_title.pack(pady=(0, 20))

        # إطار اللعبة الرئيسي
        game_frame = tk.Frame(main_frame, bg=COLORS['secondary_bg'], relief="raised", bd=2)
        game_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # تحميل وعرض لوجو اللعبة
        try:
            game_icon = Image.open(IMAGE_PATHS['game_icon'])
            game_icon = game_icon.resize((120, 120), Image.Resampling.LANCZOS)
            self.game_icon_img = ImageTk.PhotoImage(game_icon)

            icon_label = tk.Label(game_frame, image=self.game_icon_img, bg=COLORS['secondary_bg'])
            icon_label.pack(pady=(20, 10))
        except Exception as e:
            print(f"خطأ في تحميل أيقونة اللعبة: {e}")

        # تحميل وعرض لوجو اللعبة النصي
        try:
            text_logo = Image.open(IMAGE_PATHS['game_text_logo'])
            text_logo = text_logo.resize((300, 80), Image.Resampling.LANCZOS)
            self.text_logo_img = ImageTk.PhotoImage(text_logo)

            text_logo_label = tk.Label(game_frame, image=self.text_logo_img, bg=COLORS['secondary_bg'])
            text_logo_label.pack(pady=(0, 15))
        except Exception as e:
            print(f"خطأ في تحميل لوجو اللعبة النصي: {e}")

        # وصف اللعبة
        desc_label = tk.Label(game_frame, text=GAME_INFO['description'],
                             font=("Arial", 11),
                             fg=COLORS['text_primary'], bg=COLORS['secondary_bg'],
                             justify="center", wraplength=600)
        desc_label.pack(pady=(0, 20))
        
        # شريط التقدم
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(game_frame, variable=self.progress_var, 
                                          maximum=100, length=400)
        self.progress_bar.pack(pady=(0, 10))
        self.progress_bar.pack_forget()  # إخفاء في البداية
        
        # تسمية حالة التحميل
        self.status_label = tk.Label(game_frame, text="",
                                   font=("Arial", 10),
                                   fg=COLORS['text_secondary'], bg=COLORS['secondary_bg'])
        self.status_label.pack(pady=(0, 10))

        # زر التحميل
        self.create_download_button(game_frame)

    def create_download_button(self, parent):
        # إطار الزر
        button_frame = tk.Frame(parent, bg=COLORS['secondary_bg'])
        button_frame.pack(pady=20)

        # زر التحميل مع تصميم مخصص
        self.download_btn = tk.Button(button_frame,
                                    text=MESSAGES['download_button'],
                                    font=("Arial", 14, "bold"),
                                    fg="white",
                                    bg=COLORS['accent_color'],
                                    activebackground=COLORS['accent_hover'],
                                    activeforeground="white",
                                    relief="flat",
                                    padx=30, pady=10,
                                    cursor="hand2",
                                    command=self.start_download)
        self.download_btn.pack()

        # تأثيرات الزر
        self.download_btn.bind("<Enter>", self.on_button_enter)
        self.download_btn.bind("<Leave>", self.on_button_leave)

    def on_button_enter(self, event):
        self.download_btn.configure(bg=COLORS['accent_hover'])

    def on_button_leave(self, event):
        self.download_btn.configure(bg=COLORS['accent_color'])
    
    def start_download(self):
        # تعطيل الزر أثناء التحميل
        self.download_btn.configure(state="disabled", text=MESSAGES['downloading_button'])
        self.progress_bar.pack(pady=(0, 10))
        self.status_label.configure(text=MESSAGES['download_start'])

        # بدء التحميل في خيط منفصل
        download_thread = threading.Thread(target=self.download_game)
        download_thread.daemon = True
        download_thread.start()

    def download_game(self):
        try:
            # تحديث الحالة
            self.root.after(0, lambda: self.status_label.configure(text=MESSAGES['connecting']))

            # تحديد مسار الحفظ
            output_path = os.path.join(self.download_path, f"{GAME_INFO['name']}.zip")

            # التأكد من وجود مجلد التحميل
            os.makedirs(self.download_path, exist_ok=True)

            # تحميل الملف باستخدام gdown
            self.root.after(0, lambda: self.status_label.configure(text=MESSAGES['downloading']))

            # محاكاة تقدم التحميل (يمكن تحسينها لاحقاً)
            for i in range(0, 101, 2):
                self.root.after(0, lambda p=i: self.progress_var.set(p))
                self.root.after(0, lambda p=i: self.status_label.configure(
                    text=f"{MESSAGES['downloading']} {p}%"))
                threading.Event().wait(0.05)  # تأخير قصير

            # تحميل الملف
            gdown.download(f"https://drive.google.com/uc?id={self.file_id}",
                          output_path, quiet=False)

            # تحديث الحالة عند اكتمال التحميل
            self.root.after(0, self.download_completed)

        except Exception as e:
            self.root.after(0, lambda: self.download_failed(str(e)))
    
    def download_completed(self):
        self.status_label.configure(text=MESSAGES['download_complete'])
        self.download_btn.configure(state="normal", text=MESSAGES['completed_button'],
                                  bg=COLORS['success_color'])

        # إظهار رسالة النجاح
        messagebox.showinfo(MESSAGES['success_title'],
                          f"{MESSAGES['success_message']}\n\nالمسار: {self.download_path}")

    def download_failed(self, error):
        self.status_label.configure(text=MESSAGES['download_failed'])
        self.download_btn.configure(state="normal", text=MESSAGES['retry_button'],
                                  bg=COLORS['error_color'])
        self.progress_bar.pack_forget()

        messagebox.showerror(MESSAGES['error_title'], f"حدث خطأ أثناء التحميل:\n{error}")
    
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    # التأكد من وجود المكتبات المطلوبة
    try:
        app = GameLauncher()
        app.run()
    except ImportError as e:
        print(f"خطأ: مكتبة مفقودة - {e}")
        print("يرجى تثبيت المكتبات المطلوبة باستخدام:")
        print("pip install pillow gdown requests")
