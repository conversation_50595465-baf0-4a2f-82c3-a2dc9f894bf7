import customtkinter as ctk
from tkinter import messagebox
import requests
import os
import threading
from PIL import Image, ImageTk
import gdown
import zipfile
from launcher_config import COLORS, WINDOW_CONFIG, IMAGE_PATHS, FONT_PATHS, GAME_INFO, MESSAGES

# إعداد CustomTkinter
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class GameLauncher:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title(WINDOW_CONFIG['title'])
        self.root.geometry(WINDOW_CONFIG['size'])
        self.root.resizable(WINDOW_CONFIG['resizable'], WINDOW_CONFIG['resizable'])

        # تحديد مسار الخطوط
        self.font_path = FONT_PATHS['regular']
        self.bold_font_path = FONT_PATHS['bold']

        # متغيرات التحميل
        self.download_url = GAME_INFO['download_url']
        self.file_id = GAME_INFO['file_id']
        self.download_path = os.path.join(os.path.expanduser("~"), "Downloads")

        # تحميل الصور
        self.load_images()

        self.setup_ui()

    def load_images(self):
        """تحميل جميع الصور المطلوبة"""
        try:
            # لوجو الاستوديو
            studio_logo = Image.open(IMAGE_PATHS['studio_logo'])
            studio_logo = studio_logo.resize((120, 120), Image.Resampling.LANCZOS)
            self.studio_logo_img = ctk.CTkImage(light_image=studio_logo, dark_image=studio_logo, size=(120, 120))

            # أيقونة اللعبة
            game_icon = Image.open(IMAGE_PATHS['game_icon'])
            game_icon = game_icon.resize((150, 150), Image.Resampling.LANCZOS)
            self.game_icon_img = ctk.CTkImage(light_image=game_icon, dark_image=game_icon, size=(150, 150))

            # لوجو اللعبة النصي
            text_logo = Image.open(IMAGE_PATHS['game_text_logo'])
            text_logo = text_logo.resize((400, 100), Image.Resampling.LANCZOS)
            self.text_logo_img = ctk.CTkImage(light_image=text_logo, dark_image=text_logo, size=(400, 100))

            # صورة الأزرار
            button_img = Image.open(IMAGE_PATHS['buttons_image'])
            button_img = button_img.resize((200, 60), Image.Resampling.LANCZOS)
            self.button_img = ctk.CTkImage(light_image=button_img, dark_image=button_img, size=(200, 60))

        except Exception as e:
            print(f"خطأ في تحميل الصور: {e}")
            self.studio_logo_img = None
            self.game_icon_img = None
            self.text_logo_img = None
            self.button_img = None

    def setup_ui(self):
        # إطار رئيسي
        main_frame = ctk.CTkFrame(self.root, fg_color="transparent")
        main_frame.pack(fill="both", expand=True, padx=30, pady=30)

        # لوجو الاستوديو
        if self.studio_logo_img:
            studio_label = ctk.CTkLabel(main_frame, image=self.studio_logo_img, text="")
            studio_label.pack(pady=(0, 20))

        # عنوان الاستوديو
        studio_title = ctk.CTkLabel(main_frame, text=GAME_INFO['studio'],
                                   font=ctk.CTkFont(family=self.bold_font_path, size=24, weight="bold"),
                                   text_color=COLORS['accent_color'])
        studio_title.pack(pady=(0, 30))

        # إطار اللعبة الرئيسي
        game_frame = ctk.CTkFrame(main_frame, fg_color=COLORS['secondary_bg'], corner_radius=20)
        game_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # أيقونة اللعبة
        if self.game_icon_img:
            icon_label = ctk.CTkLabel(game_frame, image=self.game_icon_img, text="")
            icon_label.pack(pady=(30, 20))

        # لوجو اللعبة النصي
        if self.text_logo_img:
            text_logo_label = ctk.CTkLabel(game_frame, image=self.text_logo_img, text="")
            text_logo_label.pack(pady=(0, 30))

        # وصف اللعبة
        desc_label = ctk.CTkLabel(game_frame, text=GAME_INFO['description'],
                                 font=ctk.CTkFont(family=self.font_path, size=16),
                                 text_color=COLORS['text_primary'],
                                 justify="center", wraplength=700)
        desc_label.pack(pady=(0, 40))
        
        # تسمية حالة التحميل
        self.status_label = ctk.CTkLabel(game_frame, text="",
                                        font=ctk.CTkFont(family=self.font_path, size=14, weight="bold"),
                                        text_color=COLORS['text_secondary'])
        self.status_label.pack(pady=(0, 30))

        # زر التحميل
        self.create_download_button(game_frame)

    def create_download_button(self, parent):
        # إطار الأزرار
        button_frame = ctk.CTkFrame(parent, fg_color="transparent")
        button_frame.pack(pady=40)

        # زر التحميل مع صورة الأزرار
        if self.button_img:
            self.download_btn = ctk.CTkButton(button_frame,
                                            text=MESSAGES['download_button'],
                                            image=self.button_img,
                                            font=ctk.CTkFont(family=self.bold_font_path, size=18, weight="bold"),
                                            text_color="white",
                                            fg_color=COLORS['accent_color'],
                                            hover_color=COLORS['accent_hover'],
                                            corner_radius=15,
                                            width=250,
                                            height=70,
                                            command=self.start_download)
        else:
            self.download_btn = ctk.CTkButton(button_frame,
                                            text=MESSAGES['download_button'],
                                            font=ctk.CTkFont(family=self.bold_font_path, size=18, weight="bold"),
                                            text_color="white",
                                            fg_color=COLORS['accent_color'],
                                            hover_color=COLORS['accent_hover'],
                                            corner_radius=15,
                                            width=250,
                                            height=70,
                                            command=self.start_download)

        self.download_btn.pack(pady=20)
    
    def start_download(self):
        # تعطيل الزر أثناء التحميل
        self.download_btn.configure(state="disabled", text=MESSAGES['downloading_button'])
        self.status_label.configure(text=MESSAGES['download_start'])

        # بدء التحميل في خيط منفصل
        download_thread = threading.Thread(target=self.download_game)
        download_thread.daemon = True
        download_thread.start()

    def download_game(self):
        try:
            # تحديث الحالة
            self.root.after(0, lambda: self.status_label.configure(text=MESSAGES['connecting']))

            # تحديد مسار الحفظ
            output_path = os.path.join(self.download_path, f"{GAME_INFO['name']}.zip")

            # التأكد من وجود مجلد التحميل
            os.makedirs(self.download_path, exist_ok=True)

            # تحديث الحالة
            self.root.after(0, lambda: self.status_label.configure(text=MESSAGES['downloading']))

            # تحميل الملف من Google Drive باستخدام gdown
            # استخدام الطريقة الصحيحة لتحميل الملفات الكبيرة
            gdown.download(f"https://drive.google.com/uc?id={self.file_id}",
                          output_path, quiet=False, fuzzy=True)

            # تحديث الحالة عند اكتمال التحميل
            self.root.after(0, self.download_completed)

        except Exception as e:
            self.root.after(0, lambda: self.download_failed(str(e)))
    
    def download_completed(self):
        self.status_label.configure(text=MESSAGES['download_complete'])
        self.download_btn.configure(state="normal", text=MESSAGES['completed_button'],
                                  fg_color=COLORS['success_color'])

        # إظهار رسالة النجاح
        messagebox.showinfo(MESSAGES['success_title'],
                          f"{MESSAGES['success_message']}\n\nالمسار: {self.download_path}")

    def download_failed(self, error):
        self.status_label.configure(text=MESSAGES['download_failed'])
        self.download_btn.configure(state="normal", text=MESSAGES['retry_button'],
                                  fg_color=COLORS['error_color'])

        messagebox.showerror(MESSAGES['error_title'], f"حدث خطأ أثناء التحميل:\n{error}")
    
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    # التأكد من وجود المكتبات المطلوبة
    try:
        app = GameLauncher()
        app.run()
    except ImportError as e:
        print(f"خطأ: مكتبة مفقودة - {e}")
        print("يرجى تثبيت المكتبات المطلوبة باستخدام:")
        print("pip install pillow gdown requests")
