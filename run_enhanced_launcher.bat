@echo off
chcp 65001 >nul
title Qyi Games Launcher Setup

echo ========================================
echo    Qyi Games Launcher - لغز دار
echo ========================================
echo.

echo [1/3] Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    pause
    exit /b 1
)
echo Python found!

echo.
echo [2/3] Installing required packages...
pip install -r requirements.txt --quiet --disable-pip-version-check
if errorlevel 1 (
    echo ERROR: Failed to install packages
    pause
    exit /b 1
)
echo Packages installed successfully!

echo.
echo [3/3] Starting Qyi Games Launcher...
echo.
python enhanced_launcher.py

if errorlevel 1 (
    echo.
    echo ERROR: Failed to start launcher
    echo Trying basic launcher...
    python game_launcher.py
)

echo.
echo Launcher closed.
pause
