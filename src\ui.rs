use eframe::egui::{self, Color32, FontId, RichText, Sense, Vec2};
use crate::config::Config;

pub struct ImageCache {
    pub studio_logo: Option<egui::TextureHandle>,
    pub game_icon: Option<egui::TextureHandle>,
    pub game_text_logo: Option<egui::TextureHandle>,
    pub button_image: Option<egui::TextureHandle>,
}

impl ImageCache {
    pub fn new() -> Self {
        Self {
            studio_logo: None,
            game_icon: None,
            game_text_logo: None,
            button_image: None,
        }
    }

    pub fn load_images(&mut self, ctx: &egui::Context, config: &Config) {
        // تحميل لوجو الاستوديو
        if let Ok(image_data) = std::fs::read(&config.image_paths.studio_logo) {
            if let Ok(image) = image::load_from_memory(&image_data) {
                let rgba = image.to_rgba8();
                let size = [image.width() as usize, image.height() as usize];
                let color_image = egui::ColorImage::from_rgba_unmultiplied(size, &rgba);
                self.studio_logo = Some(ctx.load_texture("studio_logo", color_image, Default::default()));
            }
        }

        // تحميل أيقونة اللعبة
        if let Ok(image_data) = std::fs::read(&config.image_paths.game_icon) {
            if let Ok(image) = image::load_from_memory(&image_data) {
                let rgba = image.to_rgba8();
                let size = [image.width() as usize, image.height() as usize];
                let color_image = egui::ColorImage::from_rgba_unmultiplied(size, &rgba);
                self.game_icon = Some(ctx.load_texture("game_icon", color_image, Default::default()));
            }
        }

        // تحميل لوجو اللعبة النصي
        if let Ok(image_data) = std::fs::read(&config.image_paths.game_text_logo) {
            if let Ok(image) = image::load_from_memory(&image_data) {
                let rgba = image.to_rgba8();
                let size = [image.width() as usize, image.height() as usize];
                let color_image = egui::ColorImage::from_rgba_unmultiplied(size, &rgba);
                self.game_text_logo = Some(ctx.load_texture("game_text_logo", color_image, Default::default()));
            }
        }

        // تحميل صورة الأزرار
        if let Ok(image_data) = std::fs::read(&config.image_paths.buttons_image) {
            if let Ok(image) = image::load_from_memory(&image_data) {
                let rgba = image.to_rgba8();
                let size = [image.width() as usize, image.height() as usize];
                let color_image = egui::ColorImage::from_rgba_unmultiplied(size, &rgba);
                self.button_image = Some(ctx.load_texture("button_image", color_image, Default::default()));
            }
        }
    }
}

pub fn draw_header(ui: &mut egui::Ui, config: &Config, images: &ImageCache) {
    // خلفية الشريط العلوي
    let header_rect = ui.available_rect_before_wrap();
    ui.painter().rect_filled(
        header_rect,
        egui::Rounding::same(10.0),
        config.colors.secondary_bg,
    );

    ui.horizontal(|ui| {
        ui.add_space(20.0);
        
        // لوجو الاستوديو
        if let Some(logo) = &images.studio_logo {
            ui.add(egui::Image::new(logo).max_size(Vec2::new(60.0, 60.0)));
        }
        
        ui.add_space(15.0);
        
        ui.vertical(|ui| {
            ui.add_space(10.0);
            
            // عنوان الاستوديو
            ui.label(
                RichText::new(&config.game_info.studio)
                    .font(FontId::proportional(20.0))
                    .color(config.colors.accent_color)
                    .strong(),
            );
            
            // رقم الإصدار
            ui.label(
                RichText::new(format!("الإصدار {}", config.game_info.version))
                    .font(FontId::proportional(12.0))
                    .color(config.colors.text_secondary),
            );
        });
    });
    
    ui.add_space(20.0);
}

pub fn draw_game_section(
    ui: &mut egui::Ui,
    config: &Config,
    images: &ImageCache,
    status_text: &str,
    button_text: &str,
    button_enabled: bool,
    button_color: Color32,
) -> bool {
    let mut button_clicked = false;
    
    // إطار اللعبة الرئيسي
    egui::Frame::none()
        .fill(config.colors.secondary_bg)
        .rounding(egui::Rounding::same(15.0))
        .inner_margin(egui::Margin::same(30.0))
        .show(ui, |ui| {
            ui.vertical_centered(|ui| {
                // أيقونة اللعبة
                if let Some(icon) = &images.game_icon {
                    ui.add(egui::Image::new(icon).max_size(Vec2::new(150.0, 150.0)));
                    ui.add_space(20.0);
                }
                
                // لوجو اللعبة النصي
                if let Some(text_logo) = &images.game_text_logo {
                    ui.add(egui::Image::new(text_logo).max_size(Vec2::new(400.0, 100.0)));
                    ui.add_space(30.0);
                }
                
                // وصف اللعبة
                ui.label(
                    RichText::new(&config.game_info.description)
                        .font(FontId::proportional(16.0))
                        .color(config.colors.text_primary),
                );
                
                ui.add_space(40.0);
                
                // نص الحالة
                if !status_text.is_empty() {
                    ui.label(
                        RichText::new(status_text)
                            .font(FontId::proportional(14.0))
                            .color(config.colors.text_secondary)
                            .strong(),
                    );
                    ui.add_space(20.0);
                }
                
                // زر التحميل
                let button_size = Vec2::new(250.0, 70.0);
                
                // رسم الزر مع صورة الخلفية
                let (rect, response) = ui.allocate_exact_size(button_size, Sense::click());
                
                if button_enabled {
                    // تأثير التمرير
                    let button_bg_color = if response.hovered() {
                        config.colors.accent_hover
                    } else {
                        button_color
                    };
                    
                    // رسم خلفية الزر
                    ui.painter().rect_filled(
                        rect,
                        egui::Rounding::same(15.0),
                        button_bg_color,
                    );
                    
                    // رسم صورة الزر إذا كانت متوفرة
                    if let Some(button_img) = &images.button_image {
                        ui.painter().image(
                            button_img.id(),
                            rect,
                            egui::Rect::from_min_max(egui::pos2(0.0, 0.0), egui::pos2(1.0, 1.0)),
                            Color32::WHITE,
                        );
                    }
                    
                    if response.clicked() {
                        button_clicked = true;
                    }
                } else {
                    // زر معطل
                    ui.painter().rect_filled(
                        rect,
                        egui::Rounding::same(15.0),
                        Color32::GRAY,
                    );
                }
                
                // نص الزر
                ui.painter().text(
                    rect.center(),
                    egui::Align2::CENTER_CENTER,
                    button_text,
                    FontId::proportional(18.0),
                    Color32::WHITE,
                );
            });
        });
    
    button_clicked
}

pub fn draw_footer(ui: &mut egui::Ui, config: &Config) {
    ui.add_space(20.0);
    
    // خط فاصل
    ui.separator();
    
    ui.add_space(10.0);
    
    ui.horizontal(|ui| {
        // معلومات حقوق الطبع
        ui.label(
            RichText::new(format!("© 2024 {} - جميع الحقوق محفوظة", config.game_info.studio))
                .font(FontId::proportional(10.0))
                .color(config.colors.text_secondary),
        );
        
        ui.with_layout(egui::Layout::right_to_left(egui::Align::Center), |ui| {
            // رابط الموقع
            if ui.link(
                RichText::new("www.qyigames.com")
                    .font(FontId::proportional(10.0))
                    .color(config.colors.accent_color),
            ).clicked() {
                // فتح الرابط في المتصفح
                if let Err(e) = webbrowser::open("https://www.qyigames.com") {
                    eprintln!("فشل في فتح الرابط: {}", e);
                }
            }
        });
    });
}
