use anyhow::Result;
use futures_util::StreamExt;
use std::path::Path;
use tokio::fs::File;
use tokio::io::AsyncWriteExt;

pub struct Downloader {
    client: reqwest::Client,
}

#[derive(Debu<PERSON>, <PERSON>lone)]
pub enum DownloadStatus {
    Connecting,
    Downloading { progress: f32 },
    Completed,
    Failed(String),
}

impl Downloader {
    pub fn new() -> Self {
        Self {
            client: reqwest::Client::new(),
        }
    }

    pub async fn download_file<F>(
        &self,
        file_id: &str,
        output_path: &Path,
        mut progress_callback: F,
    ) -> Result<()>
    where
        F: FnMut(DownloadStatus) + Send + 'static,
    {
        progress_callback(DownloadStatus::Connecting);

        // إنشاء رابط التحميل من Google Drive
        let download_url = format!("https://drive.google.com/uc?export=download&id={}", file_id);
        
        // إرسال طلب التحميل
        let response = self.client
            .get(&download_url)
            .send()
            .await?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!("فشل في الاتصال بالخادم: {}", response.status()));
        }

        // الحصول على حجم الملف
        let total_size = response.content_length().unwrap_or(0);
        
        // إنشاء الملف
        if let Some(parent) = output_path.parent() {
            tokio::fs::create_dir_all(parent).await?;
        }
        let mut file = File::create(output_path).await?;
        
        // تحميل الملف مع تتبع التقدم
        let mut downloaded = 0u64;
        let mut stream = response.bytes_stream();
        
        while let Some(chunk) = stream.next().await {
            let chunk = chunk?;
            file.write_all(&chunk).await?;
            downloaded += chunk.len() as u64;
            
            if total_size > 0 {
                let progress = (downloaded as f32 / total_size as f32) * 100.0;
                progress_callback(DownloadStatus::Downloading { progress });
            }
        }
        
        file.flush().await?;
        progress_callback(DownloadStatus::Completed);
        
        Ok(())
    }

    pub async fn download_with_fallback<F>(
        &self,
        file_id: &str,
        output_path: &Path,
        progress_callback: F,
    ) -> Result<()>
    where
        F: FnMut(DownloadStatus) + Send + 'static + Clone,
    {
        // محاولة التحميل المباشر أولاً
        if let Err(e) = self.download_file(file_id, output_path, progress_callback.clone()).await {
            // في حالة الفشل، محاولة استخدام رابط بديل
            let alternative_url = format!("https://drive.google.com/uc?id={}&export=download&confirm=t", file_id);
            self.download_from_url(&alternative_url, output_path, progress_callback).await?;
        }
        
        Ok(())
    }

    async fn download_from_url<F>(
        &self,
        url: &str,
        output_path: &Path,
        mut progress_callback: F,
    ) -> Result<()>
    where
        F: FnMut(DownloadStatus) + Send + 'static,
    {
        progress_callback(DownloadStatus::Connecting);

        let response = self.client.get(url).send().await?;
        
        if !response.status().is_success() {
            return Err(anyhow::anyhow!("فشل في التحميل: {}", response.status()));
        }

        let total_size = response.content_length().unwrap_or(0);
        
        if let Some(parent) = output_path.parent() {
            tokio::fs::create_dir_all(parent).await?;
        }
        let mut file = File::create(output_path).await?;
        
        let mut downloaded = 0u64;
        let mut stream = response.bytes_stream();
        
        while let Some(chunk) = stream.next().await {
            let chunk = chunk?;
            file.write_all(&chunk).await?;
            downloaded += chunk.len() as u64;
            
            if total_size > 0 {
                let progress = (downloaded as f32 / total_size as f32) * 100.0;
                progress_callback(DownloadStatus::Downloading { progress });
            }
        }
        
        file.flush().await?;
        progress_callback(DownloadStatus::Completed);
        
        Ok(())
    }
}

pub fn get_download_path() -> std::path::PathBuf {
    if let Some(downloads_dir) = dirs::download_dir() {
        downloads_dir
    } else {
        // مسار احتياطي
        dirs::home_dir()
            .unwrap_or_else(|| std::path::PathBuf::from("."))
            .join("Downloads")
    }
}

#[cfg(windows)]
pub fn open_folder(path: &Path) -> Result<()> {
    use std::ffi::OsStr;
    use std::os::windows::ffi::OsStrExt;
    use winapi::um::shellapi::ShellExecuteW;
    use winapi::um::winuser::SW_SHOW;

    let path_wide: Vec<u16> = OsStr::new(path)
        .encode_wide()
        .chain(std::iter::once(0))
        .collect();

    unsafe {
        ShellExecuteW(
            std::ptr::null_mut(),
            std::ptr::null(),
            path_wide.as_ptr(),
            std::ptr::null(),
            std::ptr::null(),
            SW_SHOW,
        );
    }

    Ok(())
}

#[cfg(not(windows))]
pub fn open_folder(path: &Path) -> Result<()> {
    std::process::Command::new("xdg-open")
        .arg(path)
        .spawn()?;
    Ok(())
}
