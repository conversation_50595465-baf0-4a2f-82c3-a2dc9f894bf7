@echo off
chcp 65001 >nul
title System Check - Qyi Games Launcher

echo ========================================
echo    System Requirements Check
echo ========================================
echo.

echo Checking system requirements for Qyi Games Launcher...
echo.

:: فحص Rust
echo [1/4] Checking Rust installation...
rustc --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Rust is NOT installed
    echo    Please run: install_rust.bat
    set RUST_OK=0
) else (
    for /f "tokens=*" %%i in ('rustc --version 2^>nul') do echo ✅ %%i
    set RUST_OK=1
)

echo.

:: فحص Cargo
echo [2/4] Checking Cargo (Rust package manager)...
cargo --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Cargo is NOT available
    set CARGO_OK=0
) else (
    for /f "tokens=*" %%i in ('cargo --version 2^>nul') do echo ✅ %%i
    set CARGO_OK=1
)

echo.

:: فحص الملفات المطلوبة
echo [3/4] Checking required files...
if exist "photo\qyi games logo.png" (
    echo ✅ Studio logo found
) else (
    echo ❌ Studio logo missing: photo\qyi games logo.png
)

if exist "photo\icon game.png" (
    echo ✅ Game icon found
) else (
    echo ❌ Game icon missing: photo\icon game.png
)

if exist "photo\text logo game.png" (
    echo ✅ Text logo found
) else (
    echo ❌ Text logo missing: photo\text logo game.png
)

if exist "photo\buttons imag.png" (
    echo ✅ Button image found
) else (
    echo ❌ Button image missing: photo\buttons imag.png
)

if exist "font\Rubik-Regular.ttf" (
    echo ✅ Arabic font found
) else (
    echo ❌ Arabic font missing: font\Rubik-Regular.ttf
)

echo.

:: فحص مساحة القرص
echo [4/4] Checking disk space...
for /f "tokens=3" %%i in ('dir /-c ^| find "bytes free"') do (
    echo ✅ Available disk space: %%i bytes
)

echo.
echo ========================================
echo    Summary
echo ========================================

if "%RUST_OK%"=="1" if "%CARGO_OK%"=="1" (
    echo ✅ System is ready for building!
    echo.
    echo Next steps:
    echo 1. Run: build_and_run.bat
    echo 2. Or run: run_debug.bat for development
) else (
    echo ❌ System is NOT ready
    echo.
    echo Required actions:
    if "%RUST_OK%"=="0" echo - Install Rust: run install_rust.bat
    echo.
    echo After installing requirements, run this check again.
)

echo.
pause
