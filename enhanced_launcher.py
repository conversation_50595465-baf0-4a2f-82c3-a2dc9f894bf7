import tkinter as tk
from tkinter import ttk, messagebox
import os
import threading
import webbrowser
from PIL import Image, ImageTk
import gdown
from launcher_config import COLORS, WINDOW_CONFIG, IMAGE_PATHS, FONT_PATHS, GAME_INFO, MESSAGES

class EnhancedGameLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title(WINDOW_CONFIG['title'])
        self.root.geometry(WINDOW_CONFIG['size'])
        self.root.configure(bg=COLORS['primary_bg'])
        self.root.resizable(False, False)
        
        # إعداد أيقونة النافذة
        try:
            self.root.iconbitmap(IMAGE_PATHS['game_icon'])
        except:
            pass
        
        # متغيرات التحميل
        self.download_path = os.path.join(os.path.expanduser("~"), "Downloads")
        self.is_downloading = False
        
        self.setup_ui()
        self.center_window()
        
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def setup_ui(self):
        # إطار رئيسي مع تدرج لوني
        main_frame = tk.Frame(self.root, bg=COLORS['primary_bg'])
        main_frame.pack(fill="both", expand=True)
        
        # شريط علوي
        self.create_header(main_frame)
        
        # المحتوى الرئيسي
        content_frame = tk.Frame(main_frame, bg=COLORS['primary_bg'])
        content_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # إطار اللعبة
        self.create_game_section(content_frame)
        
        # شريط سفلي
        self.create_footer(main_frame)
        
    def create_header(self, parent):
        """إنشاء الشريط العلوي"""
        header_frame = tk.Frame(parent, bg=COLORS['secondary_bg'], height=80)
        header_frame.pack(fill="x", padx=0, pady=0)
        header_frame.pack_propagate(False)
        
        # لوجو الاستوديو في الشريط العلوي
        try:
            studio_logo = Image.open(IMAGE_PATHS['studio_logo'])
            studio_logo = studio_logo.resize((60, 60), Image.Resampling.LANCZOS)
            self.header_logo_img = ImageTk.PhotoImage(studio_logo)
            
            logo_label = tk.Label(header_frame, image=self.header_logo_img, 
                                bg=COLORS['secondary_bg'])
            logo_label.pack(side="left", padx=20, pady=10)
        except Exception as e:
            print(f"خطأ في تحميل لوجو الشريط العلوي: {e}")
        
        # عنوان الاستوديو
        title_label = tk.Label(header_frame, text=GAME_INFO['studio'], 
                             font=("Arial", 18, "bold"), 
                             fg=COLORS['accent_color'], bg=COLORS['secondary_bg'])
        title_label.pack(side="left", padx=10, pady=25)
        
        # معلومات الإصدار
        version_label = tk.Label(header_frame, text=f"الإصدار {GAME_INFO['version']}", 
                               font=("Arial", 10), 
                               fg=COLORS['text_secondary'], bg=COLORS['secondary_bg'])
        version_label.pack(side="right", padx=20, pady=30)
        
    def create_game_section(self, parent):
        """إنشاء قسم اللعبة الرئيسي"""
        game_frame = tk.Frame(parent, bg=COLORS['secondary_bg'], relief="raised", bd=3)
        game_frame.pack(fill="both", expand=True, pady=10)
        
        # أيقونة اللعبة
        self.create_game_icon(game_frame)
        
        # لوجو اللعبة النصي
        self.create_game_text_logo(game_frame)
        
        # وصف اللعبة
        self.create_game_description(game_frame)
        
        # شريط التقدم
        self.create_progress_section(game_frame)
        
        # أزرار التحكم
        self.create_control_buttons(game_frame)
        
    def create_game_icon(self, parent):
        """إنشاء أيقونة اللعبة"""
        try:
            game_icon = Image.open(IMAGE_PATHS['game_icon'])
            game_icon = game_icon.resize((140, 140), Image.Resampling.LANCZOS)
            self.game_icon_img = ImageTk.PhotoImage(game_icon)
            
            icon_label = tk.Label(parent, image=self.game_icon_img, bg=COLORS['secondary_bg'])
            icon_label.pack(pady=(30, 15))
        except Exception as e:
            print(f"خطأ في تحميل أيقونة اللعبة: {e}")
            
    def create_game_text_logo(self, parent):
        """إنشاء لوجو اللعبة النصي"""
        try:
            text_logo = Image.open(IMAGE_PATHS['game_text_logo'])
            text_logo = text_logo.resize((350, 90), Image.Resampling.LANCZOS)
            self.text_logo_img = ImageTk.PhotoImage(text_logo)
            
            text_logo_label = tk.Label(parent, image=self.text_logo_img, bg=COLORS['secondary_bg'])
            text_logo_label.pack(pady=(0, 20))
        except Exception as e:
            print(f"خطأ في تحميل لوجو اللعبة النصي: {e}")
            
    def create_game_description(self, parent):
        """إنشاء وصف اللعبة"""
        desc_frame = tk.Frame(parent, bg=COLORS['secondary_bg'])
        desc_frame.pack(pady=(0, 25), padx=40)
        
        desc_label = tk.Label(desc_frame, text=GAME_INFO['description'], 
                             font=("Arial", 12), 
                             fg=COLORS['text_primary'], bg=COLORS['secondary_bg'],
                             justify="center", wraplength=650)
        desc_label.pack()
        
    def create_progress_section(self, parent):
        """إنشاء قسم شريط التقدم"""
        progress_frame = tk.Frame(parent, bg=COLORS['secondary_bg'])
        progress_frame.pack(pady=(0, 20))
        
        # شريط التقدم
        self.progress_var = tk.DoubleVar()
        style = ttk.Style()
        style.theme_use('clam')
        style.configure("Custom.Horizontal.TProgressbar", 
                       background=COLORS['accent_color'],
                       troughcolor=COLORS['primary_bg'],
                       borderwidth=0, lightcolor=COLORS['accent_color'], 
                       darkcolor=COLORS['accent_color'])
        
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var,
                                          maximum=100, length=500,
                                          style="Custom.Horizontal.TProgressbar")
        self.progress_bar.pack(pady=(0, 10))
        self.progress_bar.pack_forget()  # إخفاء في البداية
        
        # تسمية حالة التحميل
        self.status_label = tk.Label(progress_frame, text="", 
                                   font=("Arial", 11, "bold"), 
                                   fg=COLORS['text_secondary'], bg=COLORS['secondary_bg'])
        self.status_label.pack()
        
    def create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        button_frame = tk.Frame(parent, bg=COLORS['secondary_bg'])
        button_frame.pack(pady=30)
        
        # زر التحميل الرئيسي
        self.download_btn = tk.Button(button_frame, 
                                    text=MESSAGES['download_button'], 
                                    font=("Arial", 16, "bold"),
                                    fg="white", 
                                    bg=COLORS['accent_color'],
                                    activebackground=COLORS['accent_hover'],
                                    activeforeground="white",
                                    relief="flat",
                                    padx=40, pady=15,
                                    cursor="hand2",
                                    command=self.start_download)
        self.download_btn.pack(side="left", padx=10)
        
        # زر فتح مجلد التحميل
        self.folder_btn = tk.Button(button_frame, 
                                  text="فتح مجلد التحميل", 
                                  font=("Arial", 12),
                                  fg=COLORS['text_primary'], 
                                  bg=COLORS['primary_bg'],
                                  activebackground=COLORS['secondary_bg'],
                                  activeforeground=COLORS['text_primary'],
                                  relief="flat",
                                  padx=20, pady=10,
                                  cursor="hand2",
                                  command=self.open_download_folder)
        self.folder_btn.pack(side="left", padx=10)
        
        # تأثيرات الأزرار
        self.setup_button_effects()
        
    def setup_button_effects(self):
        """إعداد تأثيرات الأزرار"""
        def on_download_enter(event):
            if not self.is_downloading:
                self.download_btn.configure(bg=COLORS['accent_hover'])
        
        def on_download_leave(event):
            if not self.is_downloading:
                self.download_btn.configure(bg=COLORS['accent_color'])
                
        def on_folder_enter(event):
            self.folder_btn.configure(bg=COLORS['secondary_bg'])
        
        def on_folder_leave(event):
            self.folder_btn.configure(bg=COLORS['primary_bg'])
        
        self.download_btn.bind("<Enter>", on_download_enter)
        self.download_btn.bind("<Leave>", on_download_leave)
        self.folder_btn.bind("<Enter>", on_folder_enter)
        self.folder_btn.bind("<Leave>", on_folder_leave)
        
    def create_footer(self, parent):
        """إنشاء الشريط السفلي"""
        footer_frame = tk.Frame(parent, bg=COLORS['primary_bg'], height=40)
        footer_frame.pack(fill="x", side="bottom")
        footer_frame.pack_propagate(False)
        
        # معلومات حقوق الطبع
        copyright_label = tk.Label(footer_frame, 
                                 text=f"© 2024 {GAME_INFO['studio']} - جميع الحقوق محفوظة", 
                                 font=("Arial", 9), 
                                 fg=COLORS['text_secondary'], bg=COLORS['primary_bg'])
        copyright_label.pack(side="left", padx=20, pady=10)
        
        # رابط الموقع (اختياري)
        website_label = tk.Label(footer_frame, text="www.qyigames.com", 
                                font=("Arial", 9, "underline"), 
                                fg=COLORS['accent_color'], bg=COLORS['primary_bg'],
                                cursor="hand2")
        website_label.pack(side="right", padx=20, pady=10)
        website_label.bind("<Button-1>", lambda e: webbrowser.open("https://www.qyigames.com"))
        
    def start_download(self):
        """بدء عملية التحميل"""
        if self.is_downloading:
            return
            
        self.is_downloading = True
        self.download_btn.configure(state="disabled", text=MESSAGES['downloading_button'],
                                  bg=COLORS['text_secondary'])
        self.progress_bar.pack(pady=(0, 10))
        self.status_label.configure(text=MESSAGES['download_start'])
        
        # بدء التحميل في خيط منفصل
        download_thread = threading.Thread(target=self.download_game)
        download_thread.daemon = True
        download_thread.start()
        
    def download_game(self):
        """تحميل اللعبة"""
        try:
            # تحديث الحالة
            self.root.after(0, lambda: self.status_label.configure(text=MESSAGES['connecting']))
            
            # تحديد مسار الحفظ
            output_path = os.path.join(self.download_path, f"{GAME_INFO['name']}.zip")
            
            # التأكد من وجود مجلد التحميل
            os.makedirs(self.download_path, exist_ok=True)
            
            # تحميل الملف
            self.root.after(0, lambda: self.status_label.configure(text=MESSAGES['downloading']))
            
            # محاكاة تقدم التحميل
            for i in range(0, 101, 1):
                self.root.after(0, lambda p=i: self.progress_var.set(p))
                self.root.after(0, lambda p=i: self.status_label.configure(
                    text=f"{MESSAGES['downloading']} {p}%"))
                threading.Event().wait(0.03)
            
            # تحميل فعلي
            gdown.download(f"https://drive.google.com/uc?id={GAME_INFO['file_id']}", 
                          output_path, quiet=False)
            
            # تحديث الحالة عند اكتمال التحميل
            self.root.after(0, self.download_completed)
            
        except Exception as e:
            self.root.after(0, lambda: self.download_failed(str(e)))
            
    def download_completed(self):
        """إكمال التحميل بنجاح"""
        self.is_downloading = False
        self.status_label.configure(text=MESSAGES['download_complete'])
        self.download_btn.configure(state="normal", text=MESSAGES['completed_button'], 
                                  bg=COLORS['success_color'])
        
        # إظهار رسالة النجاح
        messagebox.showinfo(MESSAGES['success_title'], 
                          f"{MESSAGES['success_message']}\n\nالمسار: {self.download_path}")
        
    def download_failed(self, error):
        """فشل في التحميل"""
        self.is_downloading = False
        self.status_label.configure(text=MESSAGES['download_failed'])
        self.download_btn.configure(state="normal", text=MESSAGES['retry_button'], 
                                  bg=COLORS['error_color'])
        self.progress_bar.pack_forget()
        
        messagebox.showerror(MESSAGES['error_title'], f"حدث خطأ أثناء التحميل:\n{error}")
        
    def open_download_folder(self):
        """فتح مجلد التحميل"""
        try:
            os.startfile(self.download_path)
        except:
            messagebox.showwarning("تحذير", "لا يمكن فتح مجلد التحميل")
            
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    try:
        app = EnhancedGameLauncher()
        app.run()
    except ImportError as e:
        print(f"خطأ: مكتبة مفقودة - {e}")
        print("يرجى تثبيت المكتبات المطلوبة باستخدام:")
        print("pip install pillow gdown requests")
