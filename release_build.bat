@echo off
chcp 65001 >nul
title Building Release Version - Qyi Games Launcher

echo ========================================
echo    Building Release Version
echo    Qyi Games Launcher (Rust)
echo ========================================
echo.

echo [1/4] Cleaning previous builds...
cargo clean
echo Clean completed!

echo.
echo [2/4] Optimizing for release...
echo Building with maximum optimizations...
cargo build --release
if errorlevel 1 (
    echo ERROR: Failed to build release version
    pause
    exit /b 1
)

echo.
echo [3/4] Stripping debug symbols...
strip target\release\launcher.exe 2>nul || echo Debug symbols already stripped

echo.
echo [4/4] Creating distribution package...
if not exist "dist" mkdir dist
copy "target\release\launcher.exe" "dist\"
copy "README.md" "dist\"
copy "تعليمات_سريعة.txt" "dist\"
xcopy "photo" "dist\photo\" /E /I /Y
xcopy "font" "dist\font\" /E /I /Y

echo.
echo ========================================
echo    Build Completed Successfully!
echo ========================================
echo.
echo Release files are in the 'dist' folder:
echo - launcher.exe (Main executable)
echo - README.md (Documentation)
echo - تعليمات_سريعة.txt (Quick guide)
echo - photo\ (Images folder)
echo - font\ (Fonts folder)
echo.
echo File size:
for %%I in (dist\launcher.exe) do echo - launcher.exe: %%~zI bytes
echo.
echo Ready for distribution!
pause
