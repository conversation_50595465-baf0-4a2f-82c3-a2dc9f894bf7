# Qyi Games Launcher - لانشر لعبة لغز دار

## وصف البرنامج
لانشر مخصص للعبة "لغز دار" المطورة من قبل استوديو Qyi Games. يوفر البرنامج واجهة جميلة وحديثة باستخدام CustomTkinter لتحميل وتشغيل اللعبة.

## المميزات
- واجهة مستخدم حديثة باستخدام CustomTkinter
- تصميم داكن أنيق ومتطور
- عرض لوجو الاستوديو واللعبة بجودة عالية
- وصف تفصيلي للعبة
- تحميل مباشر من Google Drive
- أزرار مخصصة بصور جميلة
- رسائل تأكيد عند اكتمال التحميل
- دعم الخطوط العربية المخصصة
- واجهة قابلة للتكبير والتصغير

## متطلبات التشغيل
- Python 3.7 أو أحدث
- نظام التشغيل Windows/Linux/macOS

## طريقة التثبيت والتشغيل

### الطريقة الأولى (تلقائية):
1. تشغيل ملف `run_launcher.bat`
2. سيقوم البرنامج بتثبيت المكتبات المطلوبة تلقائياً
3. ثم سيبدأ تشغيل اللانشر

### الطريقة الثانية (يدوية):
1. تثبيت المكتبات المطلوبة:
```bash
pip install -r requirements.txt
```

2. تشغيل البرنامج:
```bash
python game_launcher.py
```

## المكتبات المستخدمة
- **CustomTkinter**: لإنشاء واجهة مستخدم حديثة ومتطورة
- **PIL (Pillow)**: لمعالجة وعرض الصور بجودة عالية
- **gdown**: لتحميل الملفات من Google Drive
- **requests**: للاتصال بالإنترنت
- **threading**: للتحميل في الخلفية دون تجميد الواجهة

## بنية المشروع
```
Qyi Games Launcher/
├── game_launcher.py      # الملف الرئيسي للبرنامج
├── requirements.txt      # قائمة المكتبات المطلوبة
├── run_launcher.bat     # ملف تشغيل تلقائي
├── README.md           # ملف التوثيق
├── font/              # مجلد الخطوط
│   ├── Rubik-Regular.ttf
│   ├── Rubik-Bold.ttf
│   └── ...
└── photo/             # مجلد الصور
    ├── qyi games logo.png
    ├── icon game.png
    ├── text logo game.png
    └── buttons imag.png
```

## كيفية الاستخدام
1. تشغيل البرنامج
2. سيظهر لوجو الاستوديو ومعلومات اللعبة
3. النقر على زر "تحميل اللعبة"
4. انتظار اكتمال التحميل
5. ستظهر رسالة تأكيد عند انتهاء التحميل
6. الملف سيتم حفظه في مجلد Downloads

## ملاحظات
- يتم حفظ اللعبة في مجلد Downloads الافتراضي
- البرنامج يدعم اللغة العربية
- يمكن تخصيص الألوان والخطوط حسب الحاجة

## المطور
Qyi Games Studio

## الدعم الفني
في حالة وجود أي مشاكل، يرجى التأكد من:
- تثبيت Python بشكل صحيح
- وجود اتصال بالإنترنت
- تثبيت جميع المكتبات المطلوبة
