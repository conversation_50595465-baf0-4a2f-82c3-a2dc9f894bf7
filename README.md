# Qyi Games Launcher - لانشر لعبة لغز دار (Rust Edition)

## وصف البرنامج
لانشر احترافي مطور بلغة Rust للعبة "لغز دار" من استوديو Qyi Games. يوفر البرنامج أداءً فائقاً وواجهة مستخدم حديثة وجميلة باستخدام egui framework.

## المميزات الجديدة (Rust Edition)
- **أداء فائق**: مطور بلغة Rust للحصول على أفضل أداء وسرعة
- **واجهة احترافية**: باستخدام egui framework الحديث
- **استهلاك ذاكرة منخفض**: تحسين كبير في استخدام الموارد
- **أمان عالي**: الاستفادة من أمان Rust المدمج
- **تصميم متجاوب**: واجهة تتكيف مع جميع أحجام الشاشات
- **تحميل متقدم**: نظام تحميل محسن مع إدارة أفضل للأخطاء
- **دعم كامل للعربية**: خطوط عربية مدمجة وتخطيط RTL
- **رسوميات عالية الجودة**: عرض الصور بجودة فائقة
- **تأثيرات بصرية**: انيميشن وتأثيرات سلسة
- **استقرار عالي**: مقاومة للأخطاء والتجمد

## متطلبات التشغيل
- Rust 1.70 أو أحدث (للبناء من المصدر)
- نظام التشغيل Windows 10/11, Linux, أو macOS
- 4 MB مساحة فارغة على القرص الصلب
- اتصال بالإنترنت للتحميل

## طريقة التثبيت والتشغيل

### الطريقة الأولى (الأسرع - ملف تنفيذي جاهز):
1. تحميل الملف التنفيذي من قسم Releases
2. تشغيل `launcher.exe` مباشرة
3. لا حاجة لتثبيت أي شيء إضافي!

### الطريقة الثانية (البناء من المصدر):
1. تثبيت Rust من https://rustup.rs/
2. تشغيل ملف `build_and_run.bat`
3. سيقوم بالبناء والتشغيل تلقائياً

### الطريقة الثالثة (للمطورين):
```bash
# بناء الإصدار النهائي
cargo build --release

# تشغيل الإصدار النهائي
./target/release/launcher

# أو للتطوير
cargo run
```

## المكتبات المستخدمة (Rust Crates)
- **eframe/egui**: إطار عمل حديث لواجهات المستخدم الرسومية
- **tokio**: runtime غير متزامن عالي الأداء
- **reqwest**: مكتبة HTTP client متقدمة
- **image**: معالجة وعرض الصور بجودة فائقة
- **native-dialog**: نوافذ حوار نظام التشغيل
- **dirs**: الوصول لمجلدات النظام
- **anyhow**: معالجة الأخطاء المحسنة
- **futures-util**: أدوات البرمجة غير المتزامنة

## بنية المشروع
```
Qyi Games Launcher/
├── game_launcher.py      # الملف الرئيسي للبرنامج
├── requirements.txt      # قائمة المكتبات المطلوبة
├── run_launcher.bat     # ملف تشغيل تلقائي
├── README.md           # ملف التوثيق
├── font/              # مجلد الخطوط
│   ├── Rubik-Regular.ttf
│   ├── Rubik-Bold.ttf
│   └── ...
└── photo/             # مجلد الصور
    ├── qyi games logo.png
    ├── icon game.png
    ├── text logo game.png
    └── buttons imag.png
```

## كيفية الاستخدام
1. تشغيل البرنامج
2. سيظهر لوجو الاستوديو ومعلومات اللعبة
3. النقر على زر "تحميل اللعبة"
4. انتظار اكتمال التحميل
5. ستظهر رسالة تأكيد عند انتهاء التحميل
6. الملف سيتم حفظه في مجلد Downloads

## ملاحظات
- يتم حفظ اللعبة في مجلد Downloads الافتراضي
- البرنامج يدعم اللغة العربية
- يمكن تخصيص الألوان والخطوط حسب الحاجة

## المطور
Qyi Games Studio

## الدعم الفني
في حالة وجود أي مشاكل، يرجى التأكد من:
- تثبيت Python بشكل صحيح
- وجود اتصال بالإنترنت
- تثبيت جميع المكتبات المطلوبة
