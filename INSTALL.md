# دليل التثبيت - Qyi Games Launcher

## متطلبات النظام
- نظام التشغيل: Windows 10/11, macOS, أو Linux
- Python 3.7 أو أحدث
- اتصال بالإنترنت لتحميل اللعبة

## طرق التثبيت

### الطريقة الأولى: التشغيل التلقائي (الأسهل)
1. تأكد من تثبيت Python على جهازك
2. انقر نقراً مزدوجاً على ملف `run_enhanced_launcher.bat`
3. سيقوم البرنامج بتثبيت المكتبات المطلوبة تلقائياً
4. سيبدأ تشغيل اللانشر المحسن

### الطريقة الثانية: التثبيت اليدوي
```bash
# 1. تثبيت المكتبات المطلوبة
pip install -r requirements.txt

# 2. تشغيل اللانشر المحسن
python enhanced_launcher.py

# أو تشغيل اللانشر الأساسي
python game_launcher.py
```

## المكتبات المطلوبة
- **Pillow**: لمعالجة الصور
- **gdown**: لتحميل الملفات من Google Drive
- **requests**: للاتصال بالإنترنت

## إصدارات اللانشر

### اللانشر الأساسي (`game_launcher.py`)
- واجهة بسيطة وسهلة الاستخدام
- جميع الوظائف الأساسية للتحميل
- مناسب للأجهزة الضعيفة

### اللانشر المحسن (`enhanced_launcher.py`)
- واجهة أكثر جمالاً وتفصيلاً
- شريط علوي وسفلي
- زر فتح مجلد التحميل
- تأثيرات بصرية محسنة
- معلومات إضافية عن اللعبة

## استكشاف الأخطاء وإصلاحها

### خطأ: "Python is not installed"
- تأكد من تثبيت Python من الموقع الرسمي: https://python.org
- تأكد من إضافة Python إلى PATH أثناء التثبيت

### خطأ: "Failed to install packages"
- تأكد من وجود اتصال بالإنترنت
- جرب تشغيل الأمر كمدير (Run as Administrator)
- جرب تحديث pip: `python -m pip install --upgrade pip`

### خطأ: "خطأ في تحميل الصور"
- تأكد من وجود مجلد `photo` مع جميع الصور المطلوبة
- تأكد من صحة أسماء الملفات

### خطأ في التحميل من Google Drive
- تأكد من وجود اتصال بالإنترنت
- تأكد من صحة رابط Google Drive
- جرب إعادة المحاولة بعد قليل

## الملفات المطلوبة

### مجلد الصور (`photo/`)
- `qyi games logo.png` - لوجو الاستوديو
- `icon game.png` - أيقونة اللعبة
- `text logo game.png` - لوجو اللعبة النصي
- `buttons imag.png` - صور الأزرار

### مجلد الخطوط (`font/`)
- `Rubik-Regular.ttf`
- `Rubik-Bold.ttf`
- `Rubik-Medium.ttf`
- وباقي خطوط Rubik

## الدعم الفني
في حالة وجود مشاكل:
1. تأكد من تثبيت جميع المتطلبات
2. جرب إعادة تشغيل الجهاز
3. تأكد من وجود مساحة كافية على القرص الصلب
4. تأكد من عدم حجب برنامج مكافح الفيروسات للتطبيق

## معلومات إضافية
- يتم حفظ اللعبة في مجلد Downloads الافتراضي
- يمكن تغيير مسار التحميل من خلال تعديل الكود
- البرنامج يدعم اللغة العربية بالكامل
- يمكن تخصيص الألوان والتصميم من ملف `launcher_config.py`
