use eframe::egui;
use std::sync::Arc;
use tokio::sync::Mutex;
use native_dialog::{MessageDialog, MessageType};

use crate::config::Config;
use crate::downloader::{Downloader, DownloadStatus, get_download_path, open_folder};
use crate::ui::{ImageCache, draw_header, draw_game_section, draw_footer};

#[derive(Debug, Clone)]
pub enum AppState {
    Ready,
    Downloading,
    Completed,
    Failed(String),
}

pub struct LauncherApp {
    config: Config,
    images: ImageCache,
    downloader: Arc<Mutex<Downloader>>,
    state: AppState,
    status_text: String,
    button_text: String,
    download_progress: f32,
    runtime: tokio::runtime::Runtime,
}

impl LauncherApp {
    pub fn new(cc: &eframe::CreationContext<'_>) -> Self {
        let config = Config::default();
        let mut images = ImageCache::new();
        
        // تحميل الصور
        images.load_images(&cc.egui_ctx, &config);
        
        // إنشاء runtime لـ tokio
        let runtime = tokio::runtime::Runtime::new().expect("فشل في إنشاء Tokio runtime");
        
        let mut app = Self {
            config,
            images,
            downloader: Arc::new(Mutex::new(Downloader::new())),
            state: AppState::Ready,
            status_text: String::new(),
            button_text: "تحميل اللعبة".to_string(),
            download_progress: 0.0,
            runtime,
        };
        
        app.update_ui_state();
        app
    }
    
    fn update_ui_state(&mut self) {
        match &self.state {
            AppState::Ready => {
                self.status_text = String::new();
                self.button_text = self.config.messages.download_button.clone();
            }
            AppState::Downloading => {
                self.button_text = self.config.messages.downloading_button.clone();
            }
            AppState::Completed => {
                self.status_text = self.config.messages.download_complete.clone();
                self.button_text = self.config.messages.completed_button.clone();
            }
            AppState::Failed(error) => {
                self.status_text = format!("{}: {}", self.config.messages.download_failed, error);
                self.button_text = self.config.messages.retry_button.clone();
            }
        }
    }
    
    fn start_download(&mut self, ctx: egui::Context) {
        if matches!(self.state, AppState::Downloading) {
            return;
        }
        
        self.state = AppState::Downloading;
        self.status_text = self.config.messages.download_start.clone();
        self.update_ui_state();
        
        let downloader = Arc::clone(&self.downloader);
        let file_id = self.config.game_info.file_id.clone();
        let game_name = self.config.game_info.name.clone();
        let download_path = get_download_path().join(format!("{}.zip", game_name));
        
        // إنشاء قناة للتواصل مع الواجهة
        let (tx, mut rx) = tokio::sync::mpsc::unbounded_channel();
        
        // بدء التحميل في مهمة منفصلة
        self.runtime.spawn(async move {
            let downloader = downloader.lock().await;
            
            let progress_callback = {
                let tx = tx.clone();
                move |status: DownloadStatus| {
                    let _ = tx.send(status);
                }
            };
            
            if let Err(e) = downloader.download_with_fallback(&file_id, &download_path, progress_callback).await {
                let _ = tx.send(DownloadStatus::Failed(e.to_string()));
            }
        });
        
        // مراقبة حالة التحميل
        self.runtime.spawn(async move {
            while let Some(status) = rx.recv().await {
                match status {
                    DownloadStatus::Connecting => {
                        // تحديث الواجهة
                        ctx.request_repaint();
                    }
                    DownloadStatus::Downloading { progress } => {
                        // تحديث التقدم
                        ctx.request_repaint();
                    }
                    DownloadStatus::Completed => {
                        // التحميل مكتمل
                        ctx.request_repaint();
                        break;
                    }
                    DownloadStatus::Failed(error) => {
                        // فشل التحميل
                        ctx.request_repaint();
                        break;
                    }
                }
            }
        });
    }
    
    fn show_success_dialog(&self) {
        let download_path = get_download_path();
        let message = format!(
            "{}\n\nالمسار: {}",
            self.config.messages.success_message,
            download_path.display()
        );
        
        MessageDialog::new()
            .set_type(MessageType::Info)
            .set_title(&self.config.messages.success_title)
            .set_text(&message)
            .show_alert()
            .ok();
    }
    
    fn show_error_dialog(&self, error: &str) {
        let message = format!("حدث خطأ أثناء التحميل:\n{}", error);
        
        MessageDialog::new()
            .set_type(MessageType::Error)
            .set_title(&self.config.messages.error_title)
            .set_text(&message)
            .show_alert()
            .ok();
    }
    
    fn open_downloads_folder(&self) {
        let download_path = get_download_path();
        if let Err(e) = open_folder(&download_path) {
            eprintln!("فشل في فتح مجلد التحميل: {}", e);
        }
    }
}

impl eframe::App for LauncherApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        // تطبيق الثيم الداكن
        ctx.set_visuals(egui::Visuals::dark());
        
        // تخصيص الألوان
        let mut visuals = ctx.style().visuals.clone();
        visuals.window_fill = self.config.colors.primary_bg;
        visuals.panel_fill = self.config.colors.primary_bg;
        ctx.set_visuals(visuals);
        
        egui::CentralPanel::default().show(ctx, |ui| {
            ui.vertical(|ui| {
                // الشريط العلوي
                draw_header(ui, &self.config, &self.images);
                
                ui.add_space(30.0);
                
                // قسم اللعبة الرئيسي
                let button_enabled = !matches!(self.state, AppState::Downloading);
                let button_color = match &self.state {
                    AppState::Completed => self.config.colors.success_color,
                    AppState::Failed(_) => self.config.colors.error_color,
                    _ => self.config.colors.accent_color,
                };
                
                let button_clicked = draw_game_section(
                    ui,
                    &self.config,
                    &self.images,
                    &self.status_text,
                    &self.button_text,
                    button_enabled,
                    button_color,
                );
                
                if button_clicked {
                    match &self.state {
                        AppState::Ready | AppState::Failed(_) => {
                            self.start_download(ctx.clone());
                        }
                        AppState::Completed => {
                            self.show_success_dialog();
                        }
                        _ => {}
                    }
                }
                
                ui.add_space(20.0);
                
                // زر فتح مجلد التحميل
                if ui.button("فتح مجلد التحميل").clicked() {
                    self.open_downloads_folder();
                }
                
                // الشريط السفلي
                ui.with_layout(egui::Layout::bottom_up(egui::Align::Center), |ui| {
                    draw_footer(ui, &self.config);
                });
            });
        });
    }
}
