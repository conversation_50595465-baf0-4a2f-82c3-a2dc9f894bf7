🎮 لانشر لعبة لغز دار - Qyi Games Launcher 🎮

📋 طرق التشغيل السريع:

1️⃣ اللانشر المحسن (الأفضل):
   - انقر نقراً مزدوجاً على: quick_start.bat
   - أو: run_enhanced_launcher.bat

2️⃣ اللانشر الأساسي:
   - انقر نقراً مزدوجاً على: basic_launcher.bat
   - أو: run_launcher.bat

3️⃣ التشغيل اليدوي:
   - افتح موجه الأوامر في هذا المجلد
   - اكتب: python enhanced_launcher.py
   - أو: python game_launcher.py

🔧 في حالة وجود مشاكل:
   - تأكد من تثبيت Python
   - شغل: run_enhanced_launcher.bat (سيثبت المكتبات تلقائياً)

📁 الملفات المهمة:
   - enhanced_launcher.py: اللانشر المحسن
   - game_launcher.py: اللانشر الأساسي
   - launcher_config.py: إعدادات الألوان والتصميم
   - requirements.txt: قائمة المكتبات المطلوبة

🎨 تخصيص التصميم:
   - عدل ملف launcher_config.py لتغيير الألوان
   - غير الصور في مجلد photo/
   - استخدم خطوط مختلفة من مجلد font/

📞 الدعم الفني:
   - اقرأ ملف README.md للتفاصيل الكاملة
   - اقرأ ملف INSTALL.md لحل المشاكل

🎯 مميزات اللانشر:
   ✅ واجهة جميلة باللغة العربية
   ✅ تحميل مباشر من Google Drive
   ✅ شريط تقدم للتحميل
   ✅ رسائل تأكيد
   ✅ فتح مجلد التحميل
   ✅ تصميم احترافي بألوان جذابة

🏆 طورت بواسطة Qyi Games Studio
