🎮 لانشر لعبة لغز دار - Qyi Games Launcher (Rust Edition) 🎮

📋 طرق التشغيل السريع:

1️⃣ الملف التنفيذي الجاهز (الأسرع):
   - انقر نقراً مزدوجاً على: launcher.exe
   - (بعد البناء من build_and_run.bat)

2️⃣ البناء والتشغيل التلقائي:
   - انقر نقراً مزدوجاً على: build_and_run.bat

3️⃣ التطوير والاختبار:
   - انقر نقراً مزدوجاً على: run_debug.bat

4️⃣ التشغيل اليدوي:
   - افتح موجه الأوامر في هذا المجلد
   - اكتب: cargo run

🔧 في حالة وجود مشاكل:
   - تأكد من تثبيت Rust من https://rustup.rs/
   - شغل: build_and_run.bat (سيبني ويشغل تلقائياً)
   - للتطوير: شغل run_debug.bat

📁 الملفات المهمة (Rust Edition):
   - src/main.rs: نقطة البداية الرئيسية
   - src/app.rs: منطق التطبيق الأساسي
   - src/ui.rs: واجهة المستخدم والرسوميات
   - src/downloader.rs: نظام التحميل المتقدم
   - src/config.rs: إعدادات الألوان والتصميم
   - Cargo.toml: إعدادات المشروع والمكتبات
   - build_and_run.bat: بناء وتشغيل الإصدار النهائي
   - run_debug.bat: تشغيل إصدار التطوير

🎨 تخصيص التصميم:
   - عدل ملف src/config.rs لتغيير الألوان والإعدادات
   - غير الصور في مجلد photo/
   - استخدم خطوط مختلفة من مجلد font/
   - أعد البناء بعد التعديل: cargo build --release

📞 الدعم الفني:
   - اقرأ ملف README.md للتفاصيل الكاملة
   - شغل build_and_run.bat لحل مشاكل البناء
   - للمطورين: استخدم cargo check للتحقق من الأخطاء

🎯 مميزات اللانشر الجديد (Rust Edition):
   ✅ أداء فائق وسرعة عالية
   ✅ واجهة احترافية باستخدام egui
   ✅ استهلاك ذاكرة منخفض جداً
   ✅ أمان عالي مع Rust
   ✅ تصميم متجاوب وسلس
   ✅ تحميل متقدم مع إدارة أفضل للأخطاء
   ✅ دعم كامل للعربية مع خطوط مدمجة
   ✅ رسوميات عالية الجودة
   ✅ تأثيرات بصرية وانيميشن
   ✅ استقرار عالي ومقاومة للأخطاء
   ✅ حجم ملف صغير (أقل من 10 MB)
   ✅ لا يحتاج تثبيت مكتبات إضافية

🏆 طورت بواسطة Qyi Games Studio
