🎮 لانشر لعبة لغز دار - Qyi Games Launcher 🎮

📋 طرق التشغيل السريع:

1️⃣ التشغيل التلقائي (الأفضل):
   - انقر نقراً مزدوجاً على: run_launcher.bat

2️⃣ التشغيل اليدوي:
   - افتح موجه الأوامر في هذا المجلد
   - اكتب: python game_launcher.py

🔧 في حالة وجود مشاكل:
   - تأكد من تثبيت Python
   - شغل: run_enhanced_launcher.bat (سيثبت المكتبات تلقائياً)

📁 الملفات المهمة:
   - game_launcher.py: اللانشر الرئيسي (CustomTkinter)
   - launcher_config.py: إعدادات الألوان والتصميم
   - requirements.txt: قائمة المكتبات المطلوبة
   - run_launcher.bat: ملف التشغيل التلقائي

🎨 تخصيص التصميم:
   - عدل ملف launcher_config.py لتغيير الألوان
   - غير الصور في مجلد photo/
   - استخدم خطوط مختلفة من مجلد font/

📞 الدعم الفني:
   - اقرأ ملف README.md للتفاصيل الكاملة
   - شغل run_launcher.bat لحل مشاكل المكتبات

🎯 مميزات اللانشر الجديد:
   ✅ واجهة حديثة باستخدام CustomTkinter
   ✅ تصميم داكن أنيق ومتطور
   ✅ أزرار مخصصة بصور جميلة
   ✅ تحميل مباشر من Google Drive
   ✅ رسائل تأكيد واضحة
   ✅ دعم الخطوط العربية المخصصة
   ✅ واجهة قابلة للتكبير والتصغير
   ✅ تصميم احترافي بألوان جذابة

🏆 طورت بواسطة Qyi Games Studio
