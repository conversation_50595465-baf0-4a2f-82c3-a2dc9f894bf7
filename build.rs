#[cfg(windows)]
fn main() {
    // إضافة أيقونة للتطبيق على Windows
    if std::path::Path::new("photo/icon game.png").exists() {
        // يمكن إضافة معالجة الأيقونة هنا
        println!("cargo:rerun-if-changed=photo/icon game.png");
    }
    
    // إخفاء نافذة الكونسول في الإصدار النهائي
    #[cfg(not(debug_assertions))]
    {
        println!("cargo:rustc-link-arg=/SUBSYSTEM:WINDOWS");
        println!("cargo:rustc-link-arg=/ENTRY:mainCRTStartup");
    }
}

#[cfg(not(windows))]
fn main() {
    // لا حاجة لإعدادات خاصة على الأنظمة الأخرى
}
