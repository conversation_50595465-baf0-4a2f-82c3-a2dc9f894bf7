use eframe::egui;
use std::sync::Arc;
use tokio::sync::Mutex;

mod app;
mod config;
mod downloader;
mod ui;

use app::LauncherApp;

#[tokio::main]
async fn main() -> Result<(), eframe::Error> {
    // تكوين النافذة
    let options = eframe::NativeOptions {
        viewport: egui::ViewportBuilder::default()
            .with_inner_size([1000.0, 1000.0])
            .with_min_inner_size([800.0, 600.0])
            .with_icon(load_icon())
            .with_title("Qyi Games Launcher - لغز دار"),
        ..Default::default()
    };

    // تشغيل التطبيق
    eframe::run_native(
        "Qyi Games Launcher",
        options,
        Box::new(|cc| {
            // تكوين الخطوط العربية
            setup_fonts(&cc.egui_ctx);
            
            // إنشاء التطبيق
            Box::new(LauncherApp::new(cc))
        }),
    )
}

fn setup_fonts(ctx: &egui::Context) {
    let mut fonts = egui::FontDefinitions::default();
    
    // إضافة خط Rubik العربي
    if let Ok(font_data) = std::fs::read("font/Rubik-Regular.ttf") {
        fonts.font_data.insert(
            "rubik".to_owned(),
            egui::FontData::from_owned(font_data),
        );
        
        fonts.families.entry(egui::FontFamily::Proportional)
            .or_default()
            .insert(0, "rubik".to_owned());
    }
    
    if let Ok(font_data) = std::fs::read("font/Rubik-Bold.ttf") {
        fonts.font_data.insert(
            "rubik_bold".to_owned(),
            egui::FontData::from_owned(font_data),
        );
        
        fonts.families.entry(egui::FontFamily::Name("rubik_bold".into()))
            .or_default()
            .insert(0, "rubik_bold".to_owned());
    }
    
    ctx.set_fonts(fonts);
}

fn load_icon() -> egui::IconData {
    // محاولة تحميل أيقونة اللعبة
    if let Ok(image_data) = std::fs::read("photo/icon game.png") {
        if let Ok(image) = image::load_from_memory(&image_data) {
            let rgba = image.to_rgba8();
            let (width, height) = rgba.dimensions();
            return egui::IconData {
                rgba: rgba.into_raw(),
                width,
                height,
            };
        }
    }
    
    // أيقونة افتراضية في حالة فشل التحميل
    egui::IconData {
        rgba: vec![255; 32 * 32 * 4],
        width: 32,
        height: 32,
    }
}
