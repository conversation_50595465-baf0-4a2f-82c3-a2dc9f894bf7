use eframe::egui::Color32;

pub struct Config {
    pub colors: Colors,
    pub game_info: GameInfo,
    pub image_paths: ImagePaths,
    pub messages: Messages,
}

pub struct Colors {
    pub primary_bg: Color32,
    pub secondary_bg: Color32,
    pub accent_color: Color32,
    pub accent_hover: Color32,
    pub success_color: Color32,
    pub error_color: Color32,
    pub text_primary: Color32,
    pub text_secondary: Color32,
}

pub struct GameInfo {
    pub name: String,
    pub studio: String,
    pub description: String,
    pub version: String,
    pub file_id: String,
}

pub struct ImagePaths {
    pub studio_logo: String,
    pub game_icon: String,
    pub game_text_logo: String,
    pub buttons_image: String,
}

pub struct Messages {
    pub download_button: String,
    pub downloading_button: String,
    pub download_start: String,
    pub connecting: String,
    pub downloading: String,
    pub download_complete: String,
    pub download_failed: String,
    pub retry_button: String,
    pub completed_button: String,
    pub success_message: String,
    pub error_title: String,
    pub success_title: String,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            colors: Colors {
                primary_bg: Color32::from_hex("#1a1a2e").unwrap(),
                secondary_bg: Color32::from_hex("#16213e").unwrap(),
                accent_color: Color32::from_hex("#e94560").unwrap(),
                accent_hover: Color32::from_hex("#d63447").unwrap(),
                success_color: Color32::from_hex("#28a745").unwrap(),
                error_color: Color32::from_hex("#dc3545").unwrap(),
                text_primary: Color32::WHITE,
                text_secondary: Color32::from_hex("#0eaaa7").unwrap(),
            },
            game_info: GameInfo {
                name: "لغز دار".to_string(),
                studio: "Qyi Games Studio".to_string(),
                description: "لعبة لغز دار - تجربة مثيرة ومليئة بالألغاز والتحديات\n\nاستكشف عالماً مليئاً بالأسرار والألغاز المعقدة\nحل الألغاز واكتشف أسرار الدار الغامضة\nتجربة لعب فريدة ومسلية لجميع الأعمار\n\nطورت بواسطة Qyi Games Studio".to_string(),
                version: "1.0.0".to_string(),
                file_id: "14HZdpaWzmFeGUtfwNKAsTHCA3t58kLpo".to_string(),
            },
            image_paths: ImagePaths {
                studio_logo: "photo/qyi games logo.png".to_string(),
                game_icon: "photo/icon game.png".to_string(),
                game_text_logo: "photo/text logo game.png".to_string(),
                buttons_image: "photo/buttons imag.png".to_string(),
            },
            messages: Messages {
                download_button: "تحميل اللعبة".to_string(),
                downloading_button: "جاري التحميل...".to_string(),
                download_start: "بدء التحميل...".to_string(),
                connecting: "الاتصال بخادم التحميل...".to_string(),
                downloading: "جاري تحميل اللعبة...".to_string(),
                download_complete: "تم التحميل بنجاح!".to_string(),
                download_failed: "فشل في التحميل!".to_string(),
                retry_button: "إعادة المحاولة".to_string(),
                completed_button: "تم التحميل ✓".to_string(),
                success_message: "تم تحميل لعبة 'لغز دار' بنجاح!\n\nيمكنك الآن تشغيل اللعبة.".to_string(),
                error_title: "خطأ في التحميل".to_string(),
                success_title: "تم التحميل".to_string(),
            },
        }
    }
}
