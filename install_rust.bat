@echo off
chcp 65001 >nul
title Installing Rust for Qyi Games Launcher

echo ========================================
echo    Installing Rust Programming Language
echo ========================================
echo.

echo This will install Rust which is required to build the launcher.
echo.
echo Press any key to continue or Ctrl+C to cancel...
pause >nul

echo.
echo [1/2] Downloading Rust installer...
echo.

:: تحميل مثبت Rust
curl -o rustup-init.exe https://win.rustup.rs/x86_64
if errorlevel 1 (
    echo ERROR: Failed to download Rust installer
    echo Please check your internet connection
    pause
    exit /b 1
)

echo.
echo [2/2] Running Rust installer...
echo.
echo Follow the installer instructions:
echo - Press 1 and Enter for default installation
echo - Wait for installation to complete
echo.

:: تشغيل المثبت
rustup-init.exe

echo.
echo Installation completed!
echo.
echo Please restart your command prompt and run:
echo   build_and_run.bat
echo.
echo Cleaning up...
del rustup-init.exe 2>nul

pause
