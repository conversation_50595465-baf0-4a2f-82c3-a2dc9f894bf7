[package]
name = "qyi-games-launcher"
version = "1.0.0"
edition = "2021"
authors = ["Qyi Games Studio"]
description = "Professional game launcher for لغز دار"

[dependencies]
eframe = "0.24"
egui = "0.24"
egui_extras = "0.24"
image = "0.24"
tokio = { version = "1.0", features = ["full"] }
reqwest = { version = "0.11", features = ["json", "stream"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
dirs = "5.0"
anyhow = "1.0"
futures-util = "0.3"
native-dialog = "0.7"
webbrowser = "0.8"

[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3", features = ["winuser", "shellapi"] }

[[bin]]
name = "launcher"
path = "src/main.rs"
