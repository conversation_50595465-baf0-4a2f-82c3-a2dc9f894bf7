@echo off
chcp 65001 >nul
title Building Qyi Games Launcher (Rust)

echo ========================================
echo    Building Qyi Games Launcher (Rust)
echo ========================================
echo.

echo [1/3] Checking Rust installation...
rustc --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Rust is not installed or not in PATH
    echo Please install Rust from https://rustup.rs/
    pause
    exit /b 1
)
echo Rust found!

echo.
echo [2/3] Building the launcher...
cargo build --release
if errorlevel 1 (
    echo ERROR: Failed to build the launcher
    pause
    exit /b 1
)
echo Build completed successfully!

echo.
echo [3/3] Running the launcher...
echo.
target\release\launcher.exe

if errorlevel 1 (
    echo.
    echo ERROR: Failed to run the launcher
    pause
    exit /b 1
)

echo.
echo Launcher closed.
pause
